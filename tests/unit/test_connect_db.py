import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../'))

import json
import pymysql
import re
from typing import List, Dict, Tuple, Optional
# 修复导入路径 - 从src目录导入
from src.wise_match_agents.config.sql_config import get_config
from pprint import pprint
from collections import defaultdict
# 引入 DBUtils PooledDB
from dbutils.pooled_db import PooledDB

class WiseMatchDBConnector:
    """智能匹配数据库连接器，用于获取和处理聊天历史数据"""
    

    def __init__(self, git_env: str = 'dev'):
        """
        初始化数据库连接器 (现在主要依赖连接池)
        """
        # 不再需要 _connect_to_database 和 self.connection
        # 连接将在需要时从池中获取
        self.config = get_config(git_env)
        
        # 验证配置是否有效
        if not self._validate_config():
            raise ValueError(f"数据库配置无效，请检查 {git_env} 环境的配置文件")

        # 类变量：创建一个数据库连接池实例
        # mincached: 启动时开启的空连接数量
        # maxcached: 最大空闲连接数
        # maxshared: 最大共享连接数 (0 或 None 表示所有连接都是专用的)
        # maxconnections: 最大连接数（根据并发量调整）
        # blocking: 连接池满时是否阻塞等待 (True) 或抛出异常 (False)
        # maxusage: 单个连接最大复用次数 (None 表示无限制)
        # **sql_config: 传递给 pymysql.connect 的参数
        try:
            self._pool = PooledDB(
                creator=pymysql,  # 使用 pymysql 作为连接创建器
                mincached=5,      # 启动时开启的空连接数量
                maxcached=30,     # 最大空闲连接数
                maxshared=0,      # 所有连接都是专用的
                maxconnections=50, # 最大连接数，根据你的并发需求调整
                blocking=True,    # 连接池满时阻塞等待
                maxusage=1000,    # 单个连接最大复用次数
                setsession=[],    # 开始会话前执行的命令列表
                ping=1,           # ping MySQL 服务端，检查是否服务可用
                **self.config['database']      # 你的数据库配置
            )
            print(f"✓ 数据库连接池初始化成功 (环境: {git_env})")
            print(f"  - 主机: {self.config['database']['host']}")
            print(f"  - 数据库: {self.config['database']['database']}")
            print(f"  - 端口: {self.config['database']['port']}")
        except Exception as e:
            print(f"✗ 数据库连接池初始化失败: {e}")
            raise

    def _validate_config(self) -> bool:
        """验证数据库配置是否有效"""
        required_fields = ['host', 'user', 'password', 'database', 'port']
        db_config = self.config.get('database', {})
        
        for field in required_fields:
            if not db_config.get(field):
                print(f"✗ 数据库配置缺少必要字段: {field}")
                return False
        
        return True

    def _get_connection(self):
        """从连接池获取一个数据库连接"""
        try:
            return self._pool.connection() # 这会返回一个连接池管理的连接
        except Exception as e:
            print(f"✗ 从连接池获取连接失败: {e}")
            raise

    def get_resume(self, chat_id: str) -> Optional[str]:
        """获取简历数据"""
        # 从连接池获取连接
        conn = self._get_connection()
        cursor = None
        try:
            cursor = conn.cursor() # 使用从池中获取的连接创建游标
            query = "SELECT resume FROM ai_view_prepare WHERE room_id = %s"
            cursor.execute(query, (chat_id,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            print(f"获取简历失败: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            # 关键：关闭连接对象，这会将连接返回给连接池，而不是真正关闭它
            conn.close()

    def get_input_data(self, chat_id: str) -> Tuple[List[Dict], List[Dict], Optional[str]]:
        """
        获取指定chat_id的输入数据
        Args:
            chat_id: 聊天会话ID
        Returns:
            Tuple[List[Dict], List[Dict], Optional[str]]:
            (历史数据列表, 考核数据列表, JD内容)
        """
        # 从连接池获取连接
        conn = self._get_connection()
        try:
            cursor = conn.cursor() # 使用从池中获取的连接创建游标
            # 获取聊天历史数据
            history_data = self._get_chat_history(cursor, chat_id)
            if not history_data:
                print(f"⚠ 未找到chat_id为 {chat_id} 的聊天记录")
                return [], [], None
            # 获取考核和JD数据
            examine_data, jd_data, job_name = self._get_examine_and_jd_data(cursor, chat_id)
            return history_data, examine_data, jd_data, job_name
        except pymysql.Error as e:
            print(f"✗ 数据库查询失败: {e}")
            print(f"  - chat_id: {chat_id}")
            raise
        except Exception as e:
            print(f"✗ 获取输入数据时发生错误: {e}")
            raise
        finally:
            # 关键：关闭游标和连接对象
            if 'cursor' in locals() and cursor:
                cursor.close()
            conn.close() # 返回连接到池中

    def _get_chat_history(self, cursor: pymysql.cursors.Cursor, chat_id: str) -> List[Dict]:
        """获取聊天历史数据"""
        query = "SELECT content, type, timestamp, question_index FROM ai_chat_memory WHERE conversation_id = %s ORDER BY timestamp"
        cursor.execute(query, (chat_id,))
        results = cursor.fetchall()
        if not results:
            return []
        # 找到最早的时间作为基准时间
        earliest_time = results[0][2]
        # 格式化数据，将时间转换为相对秒数
        history_data = []
        for row in results:
            time_diff = (row[2] - earliest_time).total_seconds()
            history_data.append({
                'content': row[0],
                'role': row[1],
                'time': int(time_diff),
                'question_index': row[3]
            })
        return history_data

    def _get_examine_and_jd_data(self, cursor: pymysql.cursors.Cursor, chat_id: str) -> Tuple[List[Dict], Optional[str]]:
        """获取考核数据和JD数据"""
        # 从ai_view_record表获取chat_type和对应的id
        query = "SELECT chat_type, position_id, train_id FROM ai_view_record WHERE room_id = %s"
        cursor.execute(query, (chat_id,))
        result = cursor.fetchone()
        if not result:
            print(f"⚠ 未找到chat_id为 {chat_id} 的视图记录")
            return [], None
        chat_type, position_id, train_id = result
        
        # 根据chat_type获取job_examine_id
        if chat_type == 'POSITION':
            examine_id_query = "SELECT job_examine_id FROM ai_job_position WHERE id = %s"
            cursor.execute(examine_id_query, (position_id,))
        else:  # TRAIN
            examine_id_query = "SELECT job_examine_id FROM ai_job_train WHERE id = %s"
            cursor.execute(examine_id_query, (train_id,))

        examine_id_result = cursor.fetchone()
        if not examine_id_result:
            print(f"⚠ 未找到对应的job_examine_id")
            return [], None
        job_examine_id = examine_id_result[0]
        # 获取考核内容
        examine_query = "SELECT content FROM ai_job_examine WHERE id = %s"
        cursor.execute(examine_query, (job_examine_id,))
        examine_result = cursor.fetchone()
        # 处理examine数据
        examine = []
        if examine_result and examine_result[0]:
            try:
                examine_content = json.loads(examine_result[0])
                examine = examine_content if isinstance(examine_content, list) else [examine_content]
            except (json.JSONDecodeError, TypeError):
                examine = [{"content": examine_result[0]}]
        # 获取JD内容
        if chat_type == 'POSITION':
            jd_query = "SELECT content, position FROM ai_job_position WHERE id = %s"
            cursor.execute(jd_query, (position_id,))
        else:  # TRAIN
            jd_query = "SELECT content, position FROM ai_job_train WHERE id = %s"
            cursor.execute(jd_query, (train_id,))
        jd_result = cursor.fetchone()
        jd = jd_result[0] if jd_result else None
        job_name = jd_result[1] if jd_result else None
        return examine, jd, job_name

    def get_input_block(self, chat_id: str) -> Tuple[List[List[Dict]], Dict[str, Dict], Optional[str]]:
        """
        获取处理后的输入数据块
        Args:
            chat_id: 聊天会话ID
        Returns:
            Tuple[List[List[Dict]], Dict[str, Dict], Optional[str]]:
            (分组后的历史数据, 考核维度字典, JD内容)
        """
        # 获取原始数据
        history_data, examine_data, jd_data, job_name = self.get_input_data(chat_id)
        # 处理历史数据分组
        grouped_history = self._group_history_data(history_data)
        # 处理考核数据分类
        examine_dict = self._categorize_examine_data(examine_data)
        # 清除JD数据中的不可见字符
        if jd_data:
            jd_data = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f\ufeff]', '', jd_data)
        
        return grouped_history, examine_dict, jd_data, job_name

    def _group_history_data(self, history_data: List[Dict]) -> List[List[Dict]]:
        # 按question_index分组
        groups_dict = defaultdict(list)
        for item in history_data:
            question_index = item.get('question_index')
            groups_dict[question_index].append(item)
        # 按question_index排序并转换为列表
        grouped_history = [groups_dict[key] for key in sorted(groups_dict.keys())]
        return grouped_history

    def _categorize_examine_data(self, examine_data: List[Dict]) -> Dict[str, Dict]:
        """按照维度字段将考核数据分类"""
        dimensions = [
            "身体/生理条件",
            "知识技能",
            "岗位胜任力",
            "语言表达能力",
            "性格特质/心理风险",
            "兴趣/动机",
            "价值观"
        ]
        examine_dict = {}
        for item in examine_data:
            dimension = item.get('dimension', '')
            if dimension in dimensions:
                examine_dict[dimension] = {
                    'checkPoint': item.get('checkPoint', ''),
                    'weight': item.get('weight', 0)
                }
        return examine_dict

    def get_valid_conversation_ids(self) -> List[str]:
        """
        从ai_chat_memory表获取question_index不为null且行数大于10的conversation_id并去重
        Returns:
            List[str]: 去重后的conversation_id列表
        """
        conn = self._get_connection()
        cursor = None
        try:
            cursor = conn.cursor()
            query = """
            SELECT conversation_id 
            FROM ai_chat_memory 
            WHERE question_index IS NOT NULL 
            GROUP BY conversation_id 
            HAVING COUNT(*) > 10
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row[0] for row in results] if results else []
        except Exception as e:
            print(f"获取有效conversation_id失败: {e}")
            return []
        finally:
            if cursor:
                cursor.close()
            conn.close()

    # close_connection 现在不再需要关闭实际的数据库连接，
    # 因为连接池会管理它们。这个方法可以保留为空或移除。
    # 如果保留，可以用于清理连接池本身（通常在应用关闭时），
    # 但 PooledDB 通常不需要手动清理。
    def close_connection(self) -> None:
        """关闭数据库连接 (对于连接池，通常不需要操作)"""
        # self._pool.close() # 如果需要关闭整个池，可以调用此方法，但这通常在应用结束时进行
        # print("✓ 数据库连接池已清理 (如果调用了 _pool.close)")
        # print("✓ 连接池中的连接由池自身管理")
        pass

    # 上下文管理器 __enter__ 保持不变
    def __enter__(self):
        return self

    # 修改 __exit__ 不再关闭底层连接
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 不再需要 self.close_connection() 因为连接池管理连接
        # 如果 close_connection 内部有其他清理逻辑，可以保留调用
        # self.close_connection()
        pass

# --- 安装依赖 ---
# 你需要安装 DBUtils 库:
# pip install DBUtils

def save_json(connector, file_name, chat_id):
    # 使用上下文管理器自动处理连接获取和返回
    # 测试处理后的数据块
    print("\n=== 处理后的数据块 ===")
    grouped_history, examine_dict, jd, job_name = connector.get_input_block(chat_id)

    # 创建请求数据字典
    request_data = {
        "grouped_history": json.dumps(grouped_history, ensure_ascii=False),
        "examine_data": json.dumps(examine_dict, ensure_ascii=False),
        "jd_data": str(jd),
        "job_name": str(job_name)
    }

    # 保存为JSON文件
    with open(file_name, 'w', encoding='utf-8') as f:
        json.dump(request_data, f, ensure_ascii=False, indent=2)

    print("数据已保存到 request.json 文件")


def batch_json(chat_ids):
    for chat_id in chat_ids:
        save_json(connector, f"request_list/request_{chat_id}.json", chat_id)

if __name__ == "__main__":
    # 从数据库动态获取有效的chat_id列表
    with WiseMatchDBConnector(git_env='prod') as connector:
        # chat_id_list = connector.get_valid_conversation_ids()[:1]
        chat_id_list = ["Chat_ed93495f7e674e998781"]
        print(chat_id_list[0])

        history_data, examine_data, jd_data, job_name = connector.get_input_data(chat_id_list[0])
        print(history_data)
        print(examine_data)
        print(jd_data)
        print(job_name)



        # print(f"找到 {len(chat_id_list)} 个有效的conversation_id")
        # if chat_id_list:
        #     batch_json(chat_id_list)
        # else:
        #     print("未找到有效的conversation_id")

        # chat_id_list = ["Chat_ed93495f7e674e998781"]
        batch_json(chat_id_list)




    