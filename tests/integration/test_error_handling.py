import json
import os
import glob
import re

def check_json_files():
    """
    检查outputs目录中的JSON文件：
    1. 检查dimension字段是否为空，保存Chat_{id}到empty_list
    2. 检查dimension中score字段是否有0值，保存Chat_{id}到error_list
    """
    # 初始化列表
    empty_list = []
    error_list = []
    
    # 获取outputs目录路径
    outputs_dir = "/Users/<USER>/workspace3/projects/wise-match-agents/outputs"
    
    # 获取所有JSON文件
    json_files = glob.glob(os.path.join(outputs_dir, "*.json"))
    
    for file_path in json_files:
        try:
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取文件名中的Chat_{id}
            filename = os.path.basename(file_path)
            match = re.search(r'Chat_([a-f0-9]+)', filename)
            if not match:
                continue
            chat_id = match.group(1)
            
            # 检查data字段是否存在且不为None
            if 'data' not in data or data['data'] is None:
                continue
                
            # 检查dimensions字段
            data_content = data['data']
            dimensions = data_content.get('dimensions', []) if isinstance(data_content, dict) else []
            
            # 1. 检查dimensions是否为空
            if not dimensions:
                empty_list.append(chat_id)
                continue
            
            # 2. 检查dimensions中的score字段
            has_zero_score = False
            for dimension in dimensions:
                if isinstance(dimension, dict) and 'score' in dimension:
                    if dimension['score'] == 0:
                        has_zero_score = True
                        break
            
            if has_zero_score:
                error_list.append(chat_id)
                
        except (json.JSONDecodeError, IOError) as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
        except Exception as e:
            print(f"处理文件 {file_path} 时发生未知错误: {e}")
            continue
    
    return empty_list, error_list

if __name__ == "__main__":
    empty_list, error_list = check_json_files()
    
    print(f"Dimensions字段为空的文件数量: {len(empty_list)}")
    print("Empty list (Chat IDs):")
    for chat_id in empty_list:
        print(f"  - {chat_id}")
    
    print(f"\n包含score为0的文件数量: {len(error_list)}")
    print("Error list (Chat IDs):")
    for chat_id in error_list:
        print(f"  - {chat_id}")
    
    print(f"\n总共处理的文件数量: {len(empty_list) + len(error_list)}")
