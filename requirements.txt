# Requirements file for wise-match-agents
# Generated from pyproject.toml for deployment compatibility

# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
aiohttp==3.9.1
pydantic-settings==2.10.1
pyyaml==6.0.1
attrs==23.2.0

# AI and ML dependencies
langchain-openai==0.1.8
langgraph==0.1.14

# Database dependencies
dbutils==3.1.0
pymysql==1.1.2

# Utility dependencies
rapidfuzz==3.6.1

# Development dependencies (optional - install with: pip install -r requirements.txt -r requirements-dev.txt)
# These are separated to keep production installs lean
