# 配置迁移说明

## 概述

项目配置已从 YAML 文件迁移到环境变量，提供更好的安全性和部署灵活性。

## 配置优先级

1. **环境变量优先**：如果设置了完整的数据库环境变量，系统将优先使用环境变量配置
2. **YAML 文件回退**：如果环境变量不完整，系统会回退到读取 `config/database_*.yaml` 文件
3. **推荐使用环境变量**：更安全且便于容器化部署

## 必需的环境变量

### 数据库配置
```bash
DB_HOST=your-database-host
DB_PORT=3306
DB_USER=your-username
DB_PASSWORD=your-password
DB_NAME=your-database-name
DB_CHARSET=utf8mb4
```

### 应用配置
```bash
WISE_MATCH_ENV=dev  # 或 prod
APP_HOST=0.0.0.0
APP_PORT=8000
LOG_LEVEL=DEBUG     # 或 INFO
MAX_WORKERS=4
```

### AI 模型配置
```bash
DASHSCOPE_API_KEY=your-dashscope-api-key
```

## 环境配置文件结构

```
.env.example    # 配置模板文件
.env.dev        # 开发环境配置
.env.prod       # 生产环境配置
```

## 使用方式

### 开发环境
1. 复制 `.env.example` 为 `.env.dev`
2. 修改 `.env.dev` 中的配置值
3. 设置 `WISE_MATCH_ENV=dev` 或直接运行（默认为 dev）
4. 运行应用：`python -m wise_match_agents.run_service`

### 生产环境
1. 复制 `.env.example` 为 `.env.prod`
2. 修改 `.env.prod` 中的生产环境配置
3. 设置 `WISE_MATCH_ENV=prod`
4. 运行应用：`python -m wise_match_agents.run_service --env prod`

### 自动环境文件加载
系统会根据 `WISE_MATCH_ENV` 环境变量自动加载对应的配置文件：
- `WISE_MATCH_ENV=dev` → 加载 `.env.dev`
- `WISE_MATCH_ENV=prod` → 加载 `.env.prod`
- 如果对应文件不存在，会回退到 `.env` 文件

### Docker 部署
环境变量可以通过以下方式传递：
- Docker Compose 的 `environment` 或 `env_file`
- Kubernetes 的 ConfigMap 和 Secret
- 云平台的环境变量配置

## 迁移步骤

1. ✅ 更新了 `sql_config.py` 支持环境变量优先
2. ✅ 修改了 `run_service.py` 的环境验证逻辑
3. ✅ 创建了完整的 `.env` 配置文件
4. ✅ 创建了生产环境 `.env.prod` 模板
5. ✅ 更新了 `.env.example` 文档

## 向后兼容性

- 现有的 `config/database_*.yaml` 文件仍然支持
- 如果环境变量不完整，系统会自动回退到 YAML 配置
- 可以逐步迁移，不会破坏现有部署

## 安全建议

1. **不要提交 `.env` 文件到版本控制**（已在 `.gitignore` 中）
2. **生产环境使用密钥管理服务**
3. **定期轮换数据库密码和 API 密钥**
4. **使用最小权限原则配置数据库用户**
