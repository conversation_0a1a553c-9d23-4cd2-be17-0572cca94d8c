[tool.poetry]
name = "wise-match-agents"
version = "0.1.0"
description = "智能匹配代理系统 - 基于FastAPI的面试分析服务"
authors = ["yxc <<EMAIL>>"]
readme = "README.md"
packages = [{include = "wise_match_agents", from = "src"}]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
aiohttp = "^3.9.0"
langchain-openai = "^0.1.0"
langgraph = "^0.1.0"
dbutils = "^3.0.0"
pymysql = "^1.1.2"
pydantic-settings = "^2.10.1"
rapidfuzz = "^3.14.1"
pyyaml = "^6.0.1"
attrs = "^23.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"
pre-commit = "^3.5.0"
httpx = "^0.25.2"

[tool.poetry.scripts]
wise-match-server = "wise_match_agents.run_service:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["wise_match_agents"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "dbutils.*",
    "pymysql.*",
    "langchain_openai.*",
    "langgraph.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
