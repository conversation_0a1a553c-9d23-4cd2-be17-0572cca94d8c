# Development dependencies for wise-match-agents
# Install with: pip install -r requirements-dev.txt

# Testing framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2

# Code formatting and linting
black==23.11.0
isort==5.12.0
flake8==6.1.0
flake8-docstrings==1.7.0
flake8-import-order==0.18.2

# Type checking
mypy==1.7.1
types-PyYAML==*********
types-requests==2.31.0.20240106

# Code quality tools
pre-commit==3.6.0
bandit==1.7.5
safety==2.3.5

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2

# Development utilities
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# Debugging tools
pdb++==0.10.3
icecream==2.1.3

# Load testing (for API performance testing)
locust==2.17.0

# Environment management
python-dotenv==1.0.0

# Database testing utilities
pytest-postgresql==5.0.0
factory-boy==3.3.0

# API testing
requests==2.31.0
responses==0.24.1
