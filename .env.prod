# =============================================================================
# 生产环境配置文件
# =============================================================================

# 环境标识
WISE_MATCH_ENV=prod

# 服务配置
APP_HOST=0.0.0.0
APP_PORT=8000
LOG_LEVEL=INFO
MAX_WORKERS=10

# CORS配置
CORS_ORIGINS=*

# =============================================================================
# 数据库配置 - 生产环境
# =============================================================================

DB_HOST=rm-bp1jqhhn26149521cwo.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_USER=Qinglqy
DB_PASSWORD=Qinglqy@2025
DB_NAME=wisematch
DB_CHARSET=utf8mb4

# =============================================================================
# AI模型配置
# =============================================================================

# DashScope配置 (阿里云)
DASHSCOPE_API_KEY=sk-b37b4bed73ec4762bdb8c067783a8670

# OpenAI API配置 (可选)
# OPENAI_API_KEY=your-openai-api-key
# OPENAI_BASE_URL=https://api.openai.com/v1
# OPENAI_MODEL=gpt-3.5-turbo

# LangSmith配置 (可选)
# LANGCHAIN_TRACING_V2=false
# LANGCHAIN_API_KEY=your-langsmith-api-key
# LANGCHAIN_PROJECT=wise-match-agents
