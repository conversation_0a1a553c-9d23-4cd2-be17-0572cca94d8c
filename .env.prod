# 生产环境配置文件
# 从 .env.example 复制并修改的生产环境配置
#
# 配置优先级说明：
# 1. 如果设置了完整的数据库环境变量 (DB_HOST, DB_USER, DB_PASSWORD, DB_NAME)，
#    系统将优先使用环境变量配置，不再读取 config/database_*.yaml 文件
# 2. 如果环境变量不完整，系统会回退到读取 YAML 配置文件
# 3. 推荐使用环境变量配置，更安全且便于部署

# =============================================================================
# 应用环境配置
# =============================================================================

# 环境标识
WISE_MATCH_ENV=prod

# 服务配置
APP_HOST=0.0.0.0
APP_PORT=8000
LOG_LEVEL=INFO
MAX_WORKERS=10

# CORS配置
CORS_ORIGINS=*

# =============================================================================
# 数据库配置 - 生产环境
# =============================================================================

DB_HOST=rm-bp1jqhhn26149521cwo.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_USER=Qinglqy
DB_PASSWORD=Qinglqy@2025
DB_NAME=wisematch
DB_CHARSET=utf8mb4

# =============================================================================
# AI模型配置
# =============================================================================

# DashScope配置 (阿里云)
DASHSCOPE_API_KEY=sk-b37b4bed73ec4762bdb8c067783a8670

# OpenAI API配置 (可选)
# OPENAI_API_KEY=your-openai-api-key
# OPENAI_BASE_URL=https://api.openai.com/v1
# OPENAI_MODEL=gpt-3.5-turbo

# LangSmith配置 (可选)
# LANGCHAIN_TRACING_V2=false
# LANGCHAIN_API_KEY=your-langsmith-api-key
# LANGCHAIN_PROJECT=wise-match-agents
