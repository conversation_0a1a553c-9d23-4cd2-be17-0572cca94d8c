from .model_manager import get_model_manager
from .connect_db import WiseMatchDBConnector
from .prompt_demo import CONSISTENCY_JD_PROMPT, EVALUATE_JD_PROMPT, CONSISTENCY_PROMPT, EVALUATE_PROMPT
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.graph import StateGraph, END
from typing import Dict, List, Any, Optional, TypedDict, Annotated
from langgraph.graph.message import add_messages
import json
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 维度分组
labels_list_1 = ['知识技能', '兴趣/动机', '岗位胜任力']  # 岗位强相关
labels_list_2 = ['价值观', '身体/生理条件', '性格特质/心理风险', '语言表达能力']  # 通用考核

class InterviewState(TypedDict):
    """工作流状态"""
    grouped_history: List[List[Dict]]
    examine_data: Dict[str, Dict]
    jd_data: str

    # 岗位相关维度结果 - 只有节点1更新
    knowledge: Optional[List[Dict]]
    interests: Optional[List[Dict]]
    competency: Optional[List[Dict]]

    # 通用维度结果 - 只有节点2更新
    values: Optional[List[Dict]]
    physical: Optional[List[Dict]]
    character: Optional[List[Dict]]
    language: Optional[List[Dict]]

    # 最终结果 - 只有合并节点更新
    final_results: Optional[List[Dict]]

class InterviewAnalysisWorkflow:
    """基于LangGraph的并行面试分析工作流"""

    def __init__(self, temperature: float = 0, model: str = "plus", max_workers: int = 4):
        """初始化工作流"""
        if model == "plus":
            self.llm = get_model_manager().get_plus_model(temperature=temperature)
        elif model == "simple":
            self.llm = get_model_manager().get_simple_model(temperature=temperature)
        elif model == "kimi":
            self.llm = get_model_manager().get_kimi_model(temperature=temperature)
        else:
            raise ValueError(f"Invalid model: {model}")

        # 维度映射
        self.dimension_mapping = {
            '知识技能': 'knowledge',
            '兴趣/动机': 'interests',
            '岗位胜任力': 'competency',
            '价值观': 'values',
            '身体/生理条件': 'physical',
            '性格特质/心理风险': 'character',
            '语言表达能力': 'language'
        }

        # 提示词模板
        self.consistency_jd_template = ChatPromptTemplate.from_template(CONSISTENCY_JD_PROMPT)
        self.evaluation_jd_template = ChatPromptTemplate.from_template(EVALUATE_JD_PROMPT)
        self.consistency_template = ChatPromptTemplate.from_template(CONSISTENCY_PROMPT)
        self.evaluation_template = ChatPromptTemplate.from_template(EVALUATE_PROMPT)

        self.graph = self._build_graph()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    def _process_nested_loop(self, grouped_history: List[List[Dict]], examine_data: Dict, jd_data: str, labels: str) -> List[Dict]:
        """封装的双层嵌套循环处理逻辑"""
        start_time = time.time()
        results = []

        checkpoints = examine_data.get("checkPoint", [])
        if isinstance(checkpoints, str):
            checkpoints = json.loads(checkpoints) if checkpoints else []
        checkpoints = checkpoints if isinstance(checkpoints, list) else [checkpoints]

        print(f"维度[{labels}]: 处理 {len(grouped_history)} 个对话组, {len(checkpoints)} 个考核点")

        # 双层嵌套循环
        for group_idx, group in enumerate(grouped_history):
            for checkpoint_idx, checkpoint in enumerate(checkpoints):
                evidence_result = self._analyze_consistency(group, checkpoint, jd_data, labels)

                if evidence_result["found_evidence"]:
                    evaluation_result = self._evaluate_evidence(group, checkpoint, evidence_result["evidence"], jd_data, labels)

                    result = {
                        "labels": labels,
                        "checkPoint": checkpoint.get("name", ""),
                        "underline": evidence_result["evidence"],
                        "underline_detail": evidence_result.get("evidence_detail"),
                        "summary": evaluation_result.get("summary", ""),
                        "comments": evaluation_result.get("comments", ""),
                        "score": evaluation_result.get("score", 0)
                    }
                    results.append(result)

        end_time = time.time()
        duration = end_time - start_time
        print(f"维度[{labels}]: 完成，提取 {len(results)} 个分析结果，耗时 {duration:.2f} 秒")
        return results

    def _analyze_consistency(self, history_group: List[Dict], checkpoint: Dict, jd_data: str, labels: str) -> Dict:
        """分析一致性"""
        checkpoint_name = checkpoint["name"]
        checkpoint_text = "\n".join([item.get("text", "") for item in checkpoint["content"]])

        if labels in labels_list_1:
            prompt = self.consistency_jd_template.format(
                labels=labels, history_group=json.dumps(history_group, ensure_ascii=False, indent=2),
                checkpoint_name=checkpoint_name, checkpoint_text=checkpoint_text, jd=jd_data
            )
        else:
            prompt = self.consistency_template.format(
                labels=labels, history_group=json.dumps(history_group, ensure_ascii=False, indent=2),
                checkpoint_name=checkpoint_name, checkpoint_text=checkpoint_text
            )

        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.llm.invoke(prompt)
                result = json.loads(response.content)

                evidence = result.get("underline", None)
                if result.get("has_evidence", False):
                    second = result.get("second", None)
                    if second is not None:
                        for item in history_group:
                            if item.get("role") == "user" and item.get("time") == second:
                                evidence = item.get("content", evidence)
                                break

                return {
                    "found_evidence": result.get("has_evidence", False),
                    "evidence": evidence,
                    "evidence_detail": result.get("underline_detail", None)
                }
            except json.JSONDecodeError as e:
                print(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"found_evidence": False, "evidence": None, "evidence_detail": None}
            except Exception as e:
                print(f"一致性分析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"found_evidence": False, "evidence": None, "evidence_detail": None}

    def _evaluate_evidence(self, history_group: List[Dict], checkpoint: Dict, evidence: str, jd_data: str, labels: str) -> Dict:
        """评估证据"""
        checkpoint_name = checkpoint["name"]
        score_rules = "\n".join([str(item["scoreRules"]) for item in checkpoint["content"]])

        if labels in labels_list_1:
            prompt = self.evaluation_jd_template.format(
                labels=labels, jd=jd_data, checkpoint_name=checkpoint_name, underline=evidence,
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2), score_rules=score_rules
            )
        else:
            prompt = self.evaluation_template.format(
                labels=labels, checkpoint_name=checkpoint_name, underline=evidence,
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2), score_rules=score_rules
            )

        try:
            response = self.llm.invoke(prompt)
            return json.loads(response.content)
        except Exception as e:
            print(f"证据评估失败: {e}")
            return {"summary": "评估失败", "comments": "系统评估异常", "score": 0}

    def _process_job_related_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理岗位相关维度节点 - 只更新岗位相关字段"""
        start_time = time.time()
        print("🚀 处理岗位相关维度: 知识技能 + 兴趣/动机 + 岗位胜任力")

        updates = {}

        for labels in labels_list_1:
            if labels in state["examine_data"]:
                results = self._process_nested_loop(
                    state["grouped_history"],
                    state["examine_data"][labels],
                    state["jd_data"],
                    labels
                )
                key = self.dimension_mapping[labels]
                updates[key] = results
            else:
                key = self.dimension_mapping[labels]
                updates[key] = []

        end_time = time.time()
        duration = end_time - start_time
        print(f"✅ 岗位相关维度处理完成，更新字段: {list(updates.keys())}，耗时 {duration:.2f} 秒")
        return updates

    def _process_general_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理通用维度节点 - 只更新通用维度字段"""
        start_time = time.time()
        print("🚀 处理通用维度: 价值观 + 身体/生理条件 + 性格特质/心理风险 + 语言表达能力")

        updates = {}

        for labels in labels_list_2:
            if labels in state["examine_data"]:
                results = self._process_nested_loop(
                    state["grouped_history"],
                    state["examine_data"][labels],
                    state["jd_data"],
                    labels
                )
                key = self.dimension_mapping[labels]
                updates[key] = results
            else:
                key = self.dimension_mapping[labels]
                updates[key] = []

        end_time = time.time()
        duration = end_time - start_time
        print(f"✅ 通用维度处理完成，更新字段: {list(updates.keys())}，耗时 {duration:.2f} 秒")
        return updates

    def _merge_results_node(self, state: InterviewState) -> Dict[str, Any]:
        """整合所有维度结果 - 只更新final_results字段"""
        start_time = time.time()
        print("🔄 整合所有维度结果")

        all_results = []
        dimension_keys = ['knowledge', 'interests', 'competency', 'values', 'physical', 'character', 'language']

        for key in dimension_keys:
            results = state.get(key, [])
            if results:
                all_results.extend(results)
                print(f"  - {key}: {len(results)} 个结果")

        end_time = time.time()
        duration = end_time - start_time
        print(f"✅ 整合完成，共 {len(all_results)} 个分析结果，耗时 {duration:.2f} 秒")
        return {"final_results": all_results}

    def _build_graph(self) -> StateGraph:
        """构建并行处理工作流图"""
        workflow = StateGraph(InterviewState)

        # 添加节点
        workflow.add_node("job_related_node", self._process_job_related_node)
        workflow.add_node("general_node", self._process_general_node)
        workflow.add_node("merge_results", self._merge_results_node)

        # 设置入口点 - 两个节点并行开始
        workflow.set_entry_point("job_related_node")
        workflow.set_entry_point("general_node")

        # 并行处理后合并
        workflow.add_edge("job_related_node", "merge_results")
        workflow.add_edge("general_node", "merge_results")
        workflow.add_edge("merge_results", END)

        # 编译工作流
        return workflow.compile()

    def run(self, grouped_history: List[List[Dict]], examine_data: Dict[str, Dict], jd_data: str) -> Dict:
        """运行并行处理工作流"""
        print("🎯 启动并行面试分析工作流")
        workflow_start_time = time.time()

        initial_state = InterviewState(
            grouped_history=grouped_history,
            examine_data=examine_data,
            jd_data=jd_data,
            knowledge=None,
            interests=None,
            competency=None,
            values=None,
            physical=None,
            character=None,
            language=None,
            final_results=None
        )

        print("📋 并行执行策略:")
        print("  - 节点1: 只更新岗位相关维度 (knowledge, interests, competency)")
        print("  - 节点2: 只更新通用维度 (values, physical, character, language)")
        print("  - 合并节点: 整合所有结果到 final_results")

        final_state = self.graph.invoke(initial_state)

        workflow_end_time = time.time()
        total_duration = workflow_end_time - workflow_start_time
        print(f"⏱️ 整个工作流总耗时: {total_duration:.2f} 秒")

        return {"dimensions": final_state.get("final_results", [])}


if __name__ == "__main__":
    total_start_time = time.time()

    connector = WiseMatchDBConnector()

    chat_id_list = [
        "Chat_80f0388dcc854036b2d9",
        "Chat_1fb987229ebb4bcfb01c",
        "Chat_7efa509581bf426bbbfd",
        "Chat_b5b395e0686f4fbc9f83"
    ]

    workflow = InterviewAnalysisWorkflow()

    for idx, chat_id in enumerate(chat_id_list, 1):
        chat_start_time = time.time()

        print(f"\n{'='*60}")
        print(f"🎯 处理第 {idx}/{len(chat_id_list)} 个 Chat: {chat_id}")
        print(f"{'='*60}")

        grouped_history, examine_data, jd = connector.get_input_block(chat_id)
        resume = connector.get_resume(chat_id)

        eval_report = workflow.run(grouped_history, examine_data, jd)

        output_data = {
            "chat_id": chat_id,
            "resume": resume,
            "jd": jd,
            "grouped_history": grouped_history,
            "examine_data": examine_data,
            "eval_report": eval_report
        }

        filename = f"json_reports/{chat_id}_report.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        chat_end_time = time.time()
        chat_duration = chat_end_time - chat_start_time

        print(f"\n💾 结果保存至 {filename}")
        print(f"📊 生成 {len(eval_report['dimensions'])} 个分析结果")
        print(f"⏱️ 当前 Chat 处理总耗时: {chat_duration:.2f} 秒")

    connector.close_connection()

    total_end_time = time.time()
    total_program_duration = total_end_time - total_start_time
    print(f"\n{'='*60}")
    print(f"🏁 程序执行完成！")
    print(f"⏱️ 程序总运行时间: {total_program_duration:.2f} 秒")
    print(f"{'='*60}")
