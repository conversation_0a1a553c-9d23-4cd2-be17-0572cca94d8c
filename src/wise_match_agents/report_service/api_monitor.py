import time
import threading
from collections import deque
from typing import Dict
import logging

logger = logging.getLogger(__name__)

class APIMonitor:
    """API调用监控器 - 跟踪调用次数、token消耗和QPS"""
    
    def __init__(self):
        self._lock = threading.Lock()
        # 使用deque存储最近60秒的调用记录
        self._call_history = deque()  # [(timestamp, tokens)]
        self._total_calls = 0
        self._total_tokens = 0
        self._qps_history = deque()  # 存储每分钟的QPS值
        
    def record_api_call(self, input_tokens: int = 0, output_tokens: int = 0):
        """记录一次API调用"""
        with self._lock:
            timestamp = time.time()
            total_tokens = input_tokens + output_tokens
            
            self._call_history.append((timestamp, total_tokens))
            self._total_calls += 1
            self._total_tokens += total_tokens
            
            # 清理60秒前的记录
            cutoff_time = timestamp - 60
            while self._call_history and self._call_history[0][0] < cutoff_time:
                self._call_history.popleft()
    
    def get_current_stats(self) -> Dict:
        """获取当前统计数据"""
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - 60
            
            # 统计最近60秒的数据
            recent_calls = 0
            recent_tokens = 0
            
            for timestamp, tokens in self._call_history:
                if timestamp >= cutoff_time:
                    recent_calls += 1
                    recent_tokens += tokens
            
            # 计算当前QPS (最近60秒的平均值)
            current_qps = recent_calls / 60.0 if recent_calls > 0 else 0
            
            # 更新QPS历史
            if len(self._qps_history) >= 60:  # 保留最近60个QPS值
                self._qps_history.popleft()
            self._qps_history.append(current_qps)
            
            # 计算QPS统计
            qps_values = list(self._qps_history)
            avg_qps = sum(qps_values) / len(qps_values) if qps_values else 0
            max_qps = max(qps_values) if qps_values else 0
            
            return {
                "calls_per_minute": recent_calls,
                "tokens_per_minute": recent_tokens,
                "current_qps": current_qps,
                "avg_qps": avg_qps,
                "max_qps": max_qps,
                "total_calls": self._total_calls,
                "total_tokens": self._total_tokens
            }
    
    def log_stats(self):
        """打印统计信息到日志"""
        stats = self.get_current_stats()
        logger.info(f"📊 API统计 - 每分钟调用: {stats['calls_per_minute']}, "
                   f"每分钟token: {stats['tokens_per_minute']}, "
                   f"当前QPS: {stats['current_qps']:.2f}, "
                   f"平均QPS: {stats['avg_qps']:.2f}, "
                   f"最大QPS: {stats['max_qps']:.2f}")

# 全局监控器实例
_api_monitor = None
_monitor_lock = threading.Lock()

def get_api_monitor() -> APIMonitor:
    """获取全局API监控器实例"""
    global _api_monitor
    if _api_monitor is None:
        with _monitor_lock:
            if _api_monitor is None:
                _api_monitor = APIMonitor()
    return _api_monitor
