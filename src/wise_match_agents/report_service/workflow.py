import json
import time
import logging
import os
from .model_manager import get_model_manager
from .get_prompt import PromptManager
from .api_monitor import get_api_monitor
from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, END
from typing import Dict, List, Any, Optional, TypedDict
import random

# 配置日志
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, 'log')

# 如果日志目录不存在，则创建
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='a',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

# 维度分组
labels_list_1 = ['知识技能', '兴趣/动机', '岗位胜任力']  # 岗位强相关
labels_list_2 = ['价值观', '身体/生理条件', '性格特质/心理风险', '语言表达能力']  # 通用考核

class InterviewState(TypedDict):
    """工作流状态"""
    grouped_history: List[List[Dict]]
    examine_data: Dict[str, Dict]
    jd_data: str
    job_name: str

    # 各维度结果 - 每个维度节点独立更新
    knowledge: Optional[List[Dict]]
    interests: Optional[List[Dict]]
    competency: Optional[List[Dict]]
    values: Optional[List[Dict]]
    physical: Optional[List[Dict]]
    character: Optional[List[Dict]]
    language: Optional[List[Dict]]

    # 最终结果 - 只有合并节点更新
    final_results: Optional[List[Dict]]

class InterviewAnalysisWorkflow:
    """基于LangGraph的并行面试分析工作流"""

    def __init__(self, git_env:str = 'dev', retry_max_retries:int = 5):
        """初始化工作流"""
        self.retry_max_retries = retry_max_retries  # 重试次数

        # 获取对应agents的提示词，模型，温度
        self.agent_params = PromptManager(git_env=git_env).get_agent_prompts()

        # 维度映射
        self.dimension_mapping = {
            '知识技能': 'knowledge',
            '兴趣/动机': 'interests',
            '岗位胜任力': 'competency',
            '价值观': 'values',
            '身体/生理条件': 'physical',
            '性格特质/心理风险': 'character',
            '语言表达能力': 'language'
        }

        self.graph = self._build_graph()
        
    def _process_nested_loop(self, grouped_history: List[List[Dict]], examine_data: Dict, jd_data: str, labels: str, job_name: str) -> List[Dict]:
        """封装的双层嵌套循环处理逻辑"""
        start_time = time.time()
        results = []

        checkpoints = examine_data.get("checkPoint", [])
        if isinstance(checkpoints, str):
            checkpoints = json.loads(checkpoints) if checkpoints else []
        checkpoints = checkpoints if isinstance(checkpoints, list) else [checkpoints]

        # 双层嵌套循环
        for group_idx, group in enumerate(grouped_history):
            for checkpoint_idx, checkpoint in enumerate(checkpoints):
                evidence_result = self._analyze_consistency(group, checkpoint, jd_data, labels)

                if evidence_result["found_evidence"]:
                    evaluation_result = self._evaluate_evidence(group, checkpoint, evidence_result["evidence"], jd_data, labels)

                    result = {
                        "labels": labels,
                        "checkPoint": checkpoint.get("name", ""),
                        "underline": evidence_result["evidence"],
                        "underline_detail": evidence_result.get("evidence_detail"),
                        "summary": evaluation_result.get("summary", ""),
                        "comments": evaluation_result.get("comments", ""),
                        "score": evaluation_result.get("score", 0)
                    }
                    results.append(result)

        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"维度[{labels}]: 完成，提取 {len(results)} 个分析结果，耗时 {duration:.2f} 秒")
        print(f"维度[{labels}]: 完成，提取 {len(results)} 个分析结果，耗时 {duration:.2f} 秒")
        
        return results

    def _analyze_consistency(self, history_group: List[Dict], checkpoint: Dict, jd_data: str, job_name: str, labels: str) -> Dict:
        """分析一致性"""
        checkpoint_name = checkpoint["name"]
        checkpoint_text = "\n".join([item.get("text", "") for item in checkpoint["content"]])

        if labels in labels_list_1:
            # 与岗位相关
            consistency_jd_template = ChatPromptTemplate.from_template(self.agent_params['EXAMINE_CONSISTENCY_JD']['prompt'])
            model_name = self.agent_params['EXAMINE_CONSISTENCY_JD']['model']
            temperature = self.agent_params['EXAMINE_CONSISTENCY_JD']['temperature']

            llm = get_model_manager(model_name=model_name, temperature=temperature).create_model_instance()

            prompt = consistency_jd_template.format(
                labels=labels, 
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2),
                checkpoint_name=checkpoint_name, 
                checkpoint_text=checkpoint_text, 
                jd=jd_data
            )
        else:
            # 与岗位无关
            consistency_template = ChatPromptTemplate.from_template(self.agent_params['EXAMINE_CONSISTENCY']['prompt'])
            model_name = self.agent_params['EXAMINE_CONSISTENCY']['model']
            temperature = self.agent_params['EXAMINE_CONSISTENCY']['temperature']

            llm = get_model_manager(model_name=model_name, temperature=temperature).create_model_instance()

            prompt = consistency_template.format(
                labels=labels, 
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2),
                checkpoint_name=checkpoint_name, 
                checkpoint_text=checkpoint_text
            )

        max_retries = self.retry_max_retries
        for attempt in range(max_retries):
            try:
                response = llm.invoke(prompt)
                result = json.loads(response.content)

                evidence = result.get("underline", None)
                if result.get("has_evidence", False):
                    second = result.get("second", None)
                    if second is not None:
                        for item in history_group:
                            if item.get("role") == "user" and item.get("time") == second:
                                evidence = item.get("content", evidence)
                                break

                return {
                    "found_evidence": result.get("has_evidence", False),
                    "evidence": evidence,
                    "evidence_detail": result.get("underline_detail", None)
                }
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"found_evidence": False, "evidence": None, "evidence_detail": None}
            except Exception as e:
                logger.error(f"一致性分析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"found_evidence": False, "evidence": None, "evidence_detail": None}

    def _evaluate_evidence(self, history_group: List[Dict], checkpoint: Dict, evidence: str, jd_data: str, labels: str) -> Dict:
        """评估证据 - 添加重试机制和错误处理"""
        checkpoint_name = checkpoint["name"]
        score_rules = "\n".join([str(item["scoreRules"]) for item in checkpoint["content"]])

        if labels in labels_list_1:
            # 与岗位相关
            evaluation_jd_template = ChatPromptTemplate.from_template(self.agent_params['EXAMINE_EVALUATION_JD']['prompt'])
            model_name = self.agent_params['EXAMINE_EVALUATION_JD']['model']
            temperature = self.agent_params['EXAMINE_EVALUATION_JD']['temperature']

            llm = get_model_manager(model_name=model_name, temperature=temperature).create_model_instance()

            prompt = evaluation_jd_template.format(
                labels=labels, jd=jd_data, checkpoint_name=checkpoint_name, underline=evidence,
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2), score_rules=score_rules
            )
        else:
            # 与岗位无关
            evaluation_template = ChatPromptTemplate.from_template(self.agent_params['EXAMINE_EVALUATION']['prompt'])
            model_name = self.agent_params['EXAMINE_EVALUATION']['model']
            temperature = self.agent_params['EXAMINE_EVALUATION']['temperature']

            llm = get_model_manager(model_name=model_name, temperature=temperature).create_model_instance()

            # 传入输入参数
            prompt = evaluation_template.format(
                labels=labels, 
                checkpoint_name=checkpoint_name, 
                underline=evidence,
                history_group=json.dumps(history_group, ensure_ascii=False, indent=2), 
                score_rules=score_rules
            )

        # 添加重试机制
        max_retries = self.retry_max_retries
        for attempt in range(max_retries):
            try:
                # 添加随机延迟以避免并发冲突
                if attempt > 0:
                    delay = random.uniform(0.5, 2.0) * (attempt + 1)
                    time.sleep(delay)
                    logger.info(f"证据评估重试 (尝试 {attempt + 1}/{max_retries}), 延迟 {delay:.2f} 秒")
                
                response = llm.invoke(prompt)
                result = json.loads(response.content)
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"证据评估JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"summary": "JSON解析失败", "comments": "系统JSON解析异常", "score": 0}
            except Exception as e:
                logger.error(f"证据评估失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return {"summary": "评估失败", "comments": f"系统评估异常: {str(e)}", "score": 0}
        
        return {"summary": "评估失败", "comments": "系统评估异常", "score": 0}

    def _process_single_dimension_node(self, state: InterviewState, labels: str) -> Dict[str, Any]:
        """处理单个维度节点的通用方法"""
        start_time = time.time()
        logger.info(f"🚀 处理维度: {labels}")

        if labels in state["examine_data"]:
            results = self._process_nested_loop(
                state["grouped_history"],
                state["examine_data"][labels],
                state["jd_data"],
                labels
            )
        else:
            results = []

        key = self.dimension_mapping[labels]
        
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"✅ 维度[{labels}]处理完成，更新字段: {key}，耗时 {duration:.2f} 秒")
        print(f"✅ 维度[{labels}]处理完成，更新字段: {key}，耗时 {duration:.2f} 秒")
        
        return {key: results}

    def _process_knowledge_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理知识技能维度节点"""
        return self._process_single_dimension_node(state, '知识技能')

    def _process_interests_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理兴趣/动机维度节点"""
        return self._process_single_dimension_node(state, '兴趣/动机')

    def _process_competency_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理岗位胜任力维度节点"""
        return self._process_single_dimension_node(state, '岗位胜任力')

    def _process_general_node(self, state: InterviewState) -> Dict[str, Any]:
        """处理通用考核维度节点 - 合并处理四个维度"""
        start_time = time.time()
        logger.info("🚀 处理通用考核维度节点")
        
        # 四个通用考核维度
        general_dimensions = ['价值观', '身体/生理条件', '性格特质/心理风险', '语言表达能力']
        results_update = {}
        
        for labels in general_dimensions:
            dimension_start = time.time()
            logger.info(f"  - 处理维度: {labels}")
            
            if labels in state["examine_data"]:
                results = self._process_nested_loop(
                    state["grouped_history"],
                    state["examine_data"][labels],
                    state["jd_data"],
                    labels
                )
            else:
                results = []
            
            key = self.dimension_mapping[labels]
            results_update[key] = results
            
            dimension_end = time.time()
            dimension_duration = dimension_end - dimension_start
            logger.info(f"    ✅ 维度[{labels}]处理完成，更新字段: {key}，耗时 {dimension_duration:.2f} 秒")
            print(f"    ✅ 维度[{labels}]处理完成，更新字段: {key}，耗时 {dimension_duration:.2f} 秒")
        
        end_time = time.time()
        total_duration = end_time - start_time
        logger.info(f"✅ 通用考核维度节点处理完成，总耗时 {total_duration:.2f} 秒")
        print(f"✅ 通用考核维度节点处理完成，总耗时 {total_duration:.2f} 秒")
        
        return results_update

    def _merge_results_node(self, state: InterviewState) -> Dict[str, Any]:
        """整合所有维度结果 - 只更新final_results字段"""
        logger.info("🔄 整合所有维度结果")

        all_results = []
        dimension_keys = ['knowledge', 'interests', 'competency', 'values', 'physical', 'character', 'language']

        for key in dimension_keys:
            results = state.get(key, [])
            if results:
                all_results.extend(results)
                logger.info(f"  - {key}: {len(results)} 个结果")
                print(f"  - {key}: {len(results)} 个结果")

        logger.info(f"✅ 整合完成，共 {len(all_results)} 个分析结果")
        
        # 打印API统计信息
        monitor = get_api_monitor()
        monitor.log_stats()
        
        return {"final_results": all_results}

    def _build_graph(self) -> StateGraph:
        """构建4个并行节点处理工作流图 - 3个单独维度 + 1个通用维度组合"""
        workflow = StateGraph(InterviewState)

        # 添加3个独立维度节点 + 1个通用维度组合节点
        dimension_nodes = [
            ("knowledge_node", self._process_knowledge_node),
            ("interests_node", self._process_interests_node),
            ("competency_node", self._process_competency_node),
            ("general_node", self._process_general_node)  # 合并的通用考核节点
        ]

        for node_name, node_func in dimension_nodes:
            workflow.add_node(node_name, node_func)

        # 添加合并节点
        workflow.add_node("merge_results", self._merge_results_node)

        # 设置所有维度节点为并行入口点
        for node_name, _ in dimension_nodes:
            workflow.set_entry_point(node_name)

        # 所有维度节点完成后合并
        for node_name, _ in dimension_nodes:
            workflow.add_edge(node_name, "merge_results")
        
        workflow.add_edge("merge_results", END)

        # 编译工作流
        return workflow.compile()

    def run(self, 
            grouped_history: List[List[Dict]], 
            examine_data: Dict[str, Dict], 
            jd_data: str,
            job_name: str) -> Dict:
        """图结点框架并行处理工作流 - 4节点并行"""
        logger.info("🎯 启动4节点并行面试分析工作流")
        workflow_start_time = time.time()

        initial_state = InterviewState(
            grouped_history=grouped_history,
            examine_data=examine_data,
            jd_data=jd_data,
            job_name=job_name,
            knowledge=None,
            interests=None,
            competency=None,
            values=None,
            physical=None,
            character=None,
            language=None,
            final_results=None
        )

        logger.info("📋 4节点并行执行策略:")
        logger.info("  - 独立维度节点: knowledge, interests, competency")
        logger.info("  - 通用考核节点: values, physical, character, language (for循环处理)")
        logger.info("  - 合并节点: 整合所有结果到 final_results")

        final_state = self.graph.invoke(initial_state)

        workflow_end_time = time.time()
        total_duration = workflow_end_time - workflow_start_time
        logger.info(f"⏱️ 整个工作流总耗时: {total_duration:.2f} 秒")

        return {"dimensions": final_state.get("final_results", [])}

