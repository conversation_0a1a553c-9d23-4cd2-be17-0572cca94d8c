import pymysql
from typing import List, Dict
from pprint import pprint
from dbutils.pooled_db import PooledDB
from wise_match_agents.config.sql_config import get_config

class PromptManager:
    """AI代理池Prompt管理器 - 使用连接池支持高并发"""
    
    _pool = None
    
    def __init__(self, git_env: str = 'dev'):
        """初始化数据库连接池, git_env: dev or prod 表示所处为测试环境还是线上环境"""
        
        if PromptManager._pool is None:
            self.config = get_config(git_env)
            PromptManager._pool = self._create_pool()
            print("✓ 数据库连接池初始化成功")
    
    def _create_pool(self) -> PooledDB:
        """创建数据库连接池"""
        return PooledDB(
            creator=pymysql,
            maxconnections=20,      # 最大连接数
            mincached=5,           # 初始化连接数
            maxcached=10,          # 最大缓存连接数
            maxshared=3,           # 最大共享连接数
            blocking=True,         # 连接池满时是否阻塞等待
            maxusage=None,         # 单个连接最大重复使用次数
            setsession=[],         # 开始会话前执行的命令列表
            ping=0,                # ping MySQL服务端，检查是否服务可用
            **self.config['database']
        )
    
    def get_agent_prompts(self) -> List[Dict]:
        """获取指定ID的AI代理配置信息"""
        connection = PromptManager._pool.connection()
        agent_codes = ['EXAMINE_CONSISTENCY', 'EXAMINE_EVALUATION', 
                       'EXAMINE_CONSISTENCY_JD', 'EXAMINE_EVALUATION_JD']
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 构建查询语句
            placeholders = ','.join(['%s'] * len(agent_codes))
            query = f"""
            SELECT agent_code, model, prompt, temperature 
            FROM ai_agent_pool 
            WHERE agent_code IN ({placeholders})
            """
            
            cursor.execute(query, agent_codes)
            results = cursor.fetchall()
            
            params_dict = {}

            for param in results:
                params_dict.update({param['agent_code']: {
                        'model':param['model'], 
                        'temperature':param['temperature'], 
                        'prompt':param['prompt']
                    }})

            return params_dict
            
        except Exception as e:
            print(f"✗ 查询失败: {e}")
            raise
        finally:
            cursor.close()
            connection.close()  # 归还连接到池中

if __name__ == "__main__":
    prompt_manager = PromptManager()

    agents = prompt_manager.get_agent_prompts()
    
    pprint(agents)

    print(f"✓ 成功获取 {len(agents)} 条代理配置")


