import os
import httpx
from dotenv import load_dotenv
from langchain_core.language_models import BaseChatModel
from langchain_openai import ChatOpenAI
import threading
from .api_monitor import get_api_monitor

# 加载环境变量
load_dotenv()

class MonitoredChatOpenAI(ChatOpenAI):
    """带监控功能的ChatOpenAI包装类"""
    
    def invoke(self, input, config=None, **kwargs):
        """重写invoke方法以添加监控"""
        monitor = get_api_monitor()
        
        # 调用原始方法
        response = super().invoke(input, config, **kwargs)
        
        # 记录API调用 - 估算token数量
        input_tokens = len(str(input)) // 4  # 粗略估算
        output_tokens = len(response.content) // 4  # 粗略估算
        
        monitor.record_api_call(input_tokens, output_tokens)
        
        return response

class ModelManager:
    """
    模型管理器，负责模型的基础配置与实例创建，支持连接池管理。
    """
    
    # 类级别的连接池，确保全局共享
    _http_client = None
    _lock = threading.Lock()

    def __init__(self, model_name: str = "qwen-plus-latest", temperature: float = 0.0):
        """
        初始化模型管理器。
        """
        self._model_config = {
            "model_name": model_name,
            "temperature": temperature,
            "api_key": os.environ.get("DASHSCOPE_API_KEY"),
            "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1"
        }
        
        # 确保HTTP客户端已初始化
        self._ensure_http_client()

    @classmethod
    def _ensure_http_client(cls):
        """确保HTTP客户端已初始化（线程安全）"""
        if cls._http_client is None:
            with cls._lock:
                if cls._http_client is None:
                    # 配置连接池参数
                    limits = httpx.Limits(
                        max_keepalive_connections=50,  # 最大保持连接数
                        max_connections=100,           # 最大连接数
                        keepalive_expiry=30.0         # 连接保持时间（秒）
                    )
                    
                    # 配置超时参数
                    timeout = httpx.Timeout(
                        connect=10.0,    # 连接超时
                        read=60.0,       # 读取超时
                        write=10.0,      # 写入超时
                        pool=5.0         # 连接池获取连接超时
                    )
                    
                    # 创建HTTP客户端
                    cls._http_client = httpx.Client(
                        limits=limits,
                        timeout=timeout,
                        http2=True,      # 启用HTTP/2
                        follow_redirects=True
                    )
                    print("✅ HTTP连接池已初始化")

    def create_model_instance(self) -> BaseChatModel:
        """
        创建指定类型的模型实例，使用连接池和监控
        """
        
        # 合并基础配置与额外参数
        config = self._model_config.copy()
        
        # 创建带监控的模型实例
        model_instance = MonitoredChatOpenAI(
            model=config["model_name"],
            temperature=config["temperature"],
            api_key=config["api_key"],
            base_url=config["api_base"],
            # 使用自定义HTTP客户端（连接池）
            http_client=self._http_client,
            # 添加重试配置
            max_retries=3,
            # 请求超时配置
            request_timeout=60.0,
            # 其他配置
            **{k:v for k,v in config.items() if k not in ["model_name", "temperature", "api_key", "api_base"]}
        )

        return model_instance

    def get_model(self, **kwargs) -> BaseChatModel:
        """
        获取模型实例
        """
        return self.create_model_instance(**kwargs)
    
    def test_model(self) -> bool:
        """
        测试模型是否能正常调用
        
        Returns:
            bool: 测试是否成功
        """
        try:
            model = self.create_model_instance()
            response = model.invoke("你好，请回复'测试成功了'")
            print(f"✅ 模型调用成功: {response.content}")
            return True
        except Exception as e:
            print(f"❌ 模型调用失败: {e}")
            return False
    
    @classmethod
    def close_http_client(cls):
        """关闭HTTP客户端（清理资源）"""
        if cls._http_client is not None:
            with cls._lock:
                if cls._http_client is not None:
                    cls._http_client.close()
                    cls._http_client = None
                    print("✅ HTTP连接池已关闭")

    @classmethod
    def get_connection_info(cls):
        """获取连接池状态信息"""
        if cls._http_client is not None:
            return {
                "is_closed": cls._http_client.is_closed,
                "limits": {
                    "max_keepalive_connections": cls._http_client._limits.max_keepalive_connections,
                    "max_connections": cls._http_client._limits.max_connections,
                    "keepalive_expiry": cls._http_client._limits.keepalive_expiry
                }
            }
        return {"status": "HTTP客户端未初始化"}


# 全局模型管理器实例缓存
_model_managers = {}
_manager_lock = threading.Lock()

def get_model_manager(model_name: str = "qwen-plus-latest", temperature: float = 0.0) -> ModelManager:
    """获取模型管理器实例（支持不同配置的缓存）"""
    global _model_managers
    
    # 创建缓存键
    cache_key = f"{model_name}_{temperature}"
    
    if cache_key not in _model_managers:
        with _manager_lock:
            if cache_key not in _model_managers:
                _model_managers[cache_key] = ModelManager(
                    model_name=model_name, 
                    temperature=temperature
                )
    
    return _model_managers[cache_key]

def cleanup_model_managers():
    """清理所有模型管理器资源"""
    ModelManager.close_http_client()
    global _model_managers
    _model_managers.clear()
    print("✅ 所有模型管理器已清理")

# 简洁测试代码
if __name__ == "__main__":
    print("🚀 开始测试模型管理器...")
    manager = get_model_manager(model_name='qwen-flash', temperature=0.7)
    
    # 测试连接池信息
    print("📊 连接池信息:", manager.get_connection_info())
    
    # 测试模型调用
    success = manager.test_model()
    print(f"📊 测试结果: {'成功' if success else '失败'}")
    
    # 清理资源
    cleanup_model_managers()


