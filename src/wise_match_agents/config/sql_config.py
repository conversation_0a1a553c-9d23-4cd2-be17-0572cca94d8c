import yaml
import os
from typing import Dict
from pathlib import Path
from dotenv import load_dotenv

# 根据环境加载对应的 .env 文件
def load_env_file(env: str = None):
    """根据环境加载对应的 .env 文件"""
    if env is None:
        env = os.getenv('WISE_MATCH_ENV', 'dev')

    # 尝试加载环境特定的 .env 文件
    env_file = f'.env.{env}'
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 加载环境配置文件: {env_file}")
    else:
        # 回退到默认的 .env 文件
        load_dotenv()
        print(f"✅ 加载默认环境配置文件: .env")

# 加载环境变量
load_env_file()

def get_config(env: str = 'dev') -> Dict:
    """根据环境获取数据库配置

    Args:
        env: 环境标识 ('dev' 或 'prod')

    Returns:
        Dict: 数据库配置字典

    Raises:
        ValueError: 当环境参数无效时
        RuntimeError: 当配置加载失败时
    """
    # 支持从环境变量获取环境配置
    env = os.getenv('WISE_MATCH_ENV', env)

    if env not in ['dev', 'prod']:
        raise ValueError(f"Invalid env: {env}. Must be 'dev' or 'prod'")

    # 优先从环境变量获取数据库配置
    env_config = {
        'host': os.getenv('DB_HOST'),
        'port': os.getenv('DB_PORT'),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME'),
        'charset': os.getenv('DB_CHARSET')
    }

    # 检查是否所有必需的环境变量都存在
    required_keys = ['host', 'user', 'password', 'database']
    env_values_exist = all(env_config.get(key) for key in required_keys)

    if env_values_exist:
        # 使用环境变量配置
        db_config = {}
        for key, value in env_config.items():
            if value is not None:
                if key == 'port':
                    db_config[key] = int(value)
                else:
                    db_config[key] = value

        # 设置默认值
        if 'port' not in db_config:
            db_config['port'] = 3306
        if 'charset' not in db_config:
            db_config['charset'] = 'utf8mb4'

        print(f"✅ 使用环境变量数据库配置 - 环境: {env}")
        return {'database': db_config}

    else:
        # 回退到 YAML 文件配置
        project_root = Path(__file__).parent.parent.parent.parent
        config_path = project_root / 'config' / f'database_{env}.yaml'

        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            db_config = config.get('database', {})

            # 仍然支持部分环境变量覆盖
            for key, value in env_config.items():
                if value is not None:
                    if key == 'port':
                        db_config[key] = int(value)
                    else:
                        db_config[key] = value

            print(f"✅ 使用YAML文件数据库配置 - 环境: {env}")
            return {'database': db_config}

        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML format in config file: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")


def get_app_config(env: str = 'dev') -> Dict:
    """获取应用配置

    Args:
        env: 环境标识

    Returns:
        Dict: 应用配置字典
    """
    return {
        'app': {
            'name': 'wise-match-agents',
            'version': '0.1.0',
            'debug': env == 'dev',
            'host': os.getenv('APP_HOST', '0.0.0.0'),
            'port': int(os.getenv('APP_PORT', '8000')),
            'log_level': os.getenv('LOG_LEVEL', 'INFO' if env == 'prod' else 'DEBUG'),
            'cors_origins': os.getenv('CORS_ORIGINS', '*').split(',') if os.getenv('CORS_ORIGINS') else ['*'],
            'max_workers': int(os.getenv('MAX_WORKERS', '10')),
        }
    }
