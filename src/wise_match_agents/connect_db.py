import json
import pymysql
import re
from typing import List, Dict, Tuple, Optional
from .config.sql_config import get_config
from pprint import pprint
from collections import defaultdict
# 引入 DBUtils PooledDB
from dbutils.pooled_db import PooledDB

class WiseMatchDBConnector:
    """智能匹配数据库连接器，用于获取和处理聊天历史数据"""

    # 类变量：将在类初始化时创建连接池
    _pool = None

    @classmethod
    def _initialize_pool(cls, env: str = 'dev'):
        """初始化数据库连接池"""
        if cls._pool is None:
            # 获取配置
            config = get_config(env)
            db_config = config['database']

            # 创建连接池
            # mincached: 启动时开启的空连接数量
            # maxcached: 最大空闲连接数
            # maxshared: 最大共享连接数 (0 或 None 表示所有连接都是专用的)
            # maxconnections: 最大连接数（根据并发量调整）
            # blocking: 连接池满时是否阻塞等待 (True) 或抛出异常 (False)
            # maxusage: 单个连接最大复用次数 (None 表示无限制)
            # **db_config: 传递给 pymysql.connect 的参数
            cls._pool = PooledDB(
                creator=pymysql,  # 使用 pymysql 作为连接创建器
                mincached=5,      # 启动时开启的空连接数量
                maxcached=30,     # 最大空闲连接数
                maxshared=0,      # 所有连接都是专用的
                maxconnections=50, # 最大连接数，根据你的并发需求调整
                blocking=True,    # 连接池满时阻塞等待
                maxusage=1000,    # 单个连接最大复用次数
                setsession=[],    # 开始会话前执行的命令列表
                ping=1,           # ping MySQL 服务端，检查是否服务可用
                **db_config       # 数据库配置
            )

    def __init__(self, env: str = 'dev'):
        """
        初始化数据库连接器
        Args:
            env: 环境标识 ('dev' 或 'prod')
        """
        # 初始化连接池
        self._initialize_pool(env)

    def _get_connection(self):
        """从连接池获取一个数据库连接"""
        if self._pool is None:
            raise RuntimeError("连接池未初始化")
        return self._pool.connection() # 这会返回一个连接池管理的连接

    def get_resume(self, chat_id: str) -> Optional[str]:
        """获取简历数据"""
        # 从连接池获取连接
        conn = self._get_connection()
        cursor = None
        try:
            cursor = conn.cursor() # 使用从池中获取的连接创建游标
            query = "SELECT resume FROM ai_view_prepare WHERE room_id = %s"
            cursor.execute(query, (chat_id,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            print(f"获取简历失败: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            # 关键：关闭连接对象，这会将连接返回给连接池，而不是真正关闭它
            conn.close()

    def get_input_data(self, chat_id: str) -> Tuple[List[Dict], List[Dict], Optional[str]]:
        """
        获取指定chat_id的输入数据
        Args:
            chat_id: 聊天会话ID
        Returns:
            Tuple[List[Dict], List[Dict], Optional[str]]:
            (历史数据列表, 考核数据列表, JD内容)
        """
        # 从连接池获取连接
        conn = self._get_connection()
        try:
            cursor = conn.cursor() # 使用从池中获取的连接创建游标
            # 获取聊天历史数据
            history_data = self._get_chat_history(cursor, chat_id)
            if not history_data:
                print(f"⚠ 未找到chat_id为 {chat_id} 的聊天记录")
                return [], [], None
            # 获取考核和JD数据
            examine_data, jd_data = self._get_examine_and_jd_data(cursor, chat_id)
            return history_data, examine_data, jd_data
        except pymysql.Error as e:
            print(f"✗ 数据库查询失败: {e}")
            print(f"  - chat_id: {chat_id}")
            raise
        except Exception as e:
            print(f"✗ 获取输入数据时发生错误: {e}")
            raise
        finally:
            # 关键：关闭游标和连接对象
            if 'cursor' in locals() and cursor:
                cursor.close()
            conn.close() # 返回连接到池中

    def _get_chat_history(self, cursor: pymysql.cursors.Cursor, chat_id: str) -> List[Dict]:
        """获取聊天历史数据"""
        query = "SELECT content, type, timestamp, question_index FROM ai_chat_memory WHERE conversation_id = %s ORDER BY timestamp"
        cursor.execute(query, (chat_id,))
        results = cursor.fetchall()
        if not results:
            return []
        # 找到最早的时间作为基准时间
        earliest_time = results[0][2]
        # 格式化数据，将时间转换为相对秒数
        history_data = []
        for row in results:
            time_diff = (row[2] - earliest_time).total_seconds()
            history_data.append({
                'content': row[0],
                'role': row[1],
                'time': int(time_diff),
                'question_index': row[3]
            })
        return history_data

    def _get_examine_and_jd_data(self, cursor: pymysql.cursors.Cursor, chat_id: str) -> Tuple[List[Dict], Optional[str]]:
        """获取考核数据和JD数据"""
        # 从ai_view_record表获取chat_type和对应的id
        query = "SELECT chat_type, position_id, train_id FROM ai_view_record WHERE room_id = %s"
        cursor.execute(query, (chat_id,))
        result = cursor.fetchone()
        if not result:
            print(f"⚠ 未找到chat_id为 {chat_id} 的视图记录")
            return [], None
        chat_type, position_id, train_id = result
        # 根据chat_type获取job_examine_id
        if chat_type == 'POSITION':
            examine_id_query = "SELECT job_examine_id FROM ai_job_position WHERE id = %s"
            cursor.execute(examine_id_query, (position_id,))
        else:  # TRAIN
            examine_id_query = "SELECT job_examine_id FROM ai_job_train WHERE id = %s"
            cursor.execute(examine_id_query, (train_id,))
        examine_id_result = cursor.fetchone()
        if not examine_id_result:
            print(f"⚠ 未找到对应的job_examine_id")
            return [], None
        job_examine_id = examine_id_result[0]
        # 获取考核内容
        examine_query = "SELECT content FROM ai_job_examine WHERE id = %s"
        cursor.execute(examine_query, (job_examine_id,))
        examine_result = cursor.fetchone()
        # 处理examine数据
        examine = []
        if examine_result and examine_result[0]:
            try:
                examine_content = json.loads(examine_result[0])
                examine = examine_content if isinstance(examine_content, list) else [examine_content]
            except (json.JSONDecodeError, TypeError):
                examine = [{"content": examine_result[0]}]
        # 获取JD内容
        if chat_type == 'POSITION':
            jd_query = "SELECT content FROM ai_job_position WHERE id = %s"
            cursor.execute(jd_query, (position_id,))
        else:  # TRAIN
            jd_query = "SELECT content FROM ai_job_train WHERE id = %s"
            cursor.execute(jd_query, (train_id,))
        jd_result = cursor.fetchone()
        jd = jd_result[0] if jd_result else None
        return examine, jd

    def get_input_block(self, chat_id: str) -> Tuple[List[List[Dict]], Dict[str, Dict], Optional[str]]:
        """
        获取处理后的输入数据块
        Args:
            chat_id: 聊天会话ID
        Returns:
            Tuple[List[List[Dict]], Dict[str, Dict], Optional[str]]:
            (分组后的历史数据, 考核维度字典, JD内容)
        """
        # 获取原始数据
        history_data, examine_data, jd_data = self.get_input_data(chat_id)
        # 处理历史数据分组
        grouped_history = self._group_history_data(history_data)
        # 处理考核数据分类
        examine_dict = self._categorize_examine_data(examine_data)
        # 清除JD数据中的不可见字符
        if jd_data:
            jd_data = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f\ufeff]', '', jd_data)
        return grouped_history, examine_dict, jd_data

    def _group_history_data(self, history_data: List[Dict]) -> List[List[Dict]]:
        # 按question_index分组
        groups_dict = defaultdict(list)
        for item in history_data:
            question_index = item.get('question_index')
            groups_dict[question_index].append(item)
        # 按question_index排序并转换为列表
        grouped_history = [groups_dict[key] for key in sorted(groups_dict.keys())]
        return grouped_history

    def _categorize_examine_data(self, examine_data: List[Dict]) -> Dict[str, Dict]:
        """按照维度字段将考核数据分类"""
        dimensions = [
            "身体/生理条件",
            "知识技能",
            "岗位胜任力",
            "语言表达能力",
            "性格特质/心理风险",
            "兴趣/动机",
            "价值观"
        ]
        examine_dict = {}
        for item in examine_data:
            dimension = item.get('dimension', '')
            if dimension in dimensions:
                examine_dict[dimension] = {
                    'checkPoint': item.get('checkPoint', ''),
                    'weight': item.get('weight', 0)
                }
        return examine_dict

    def close_connection(self) -> None:
        """关闭数据库连接 (对于连接池，通常不需要操作)"""
        # self._pool.close() # 如果需要关闭整个池，可以调用此方法，但这通常在应用结束时进行
        # print("✓ 数据库连接池已清理 (如果调用了 _pool.close)")
        # print("✓ 连接池中的连接由池自身管理")
        pass

    # 上下文管理器 __enter__ 保持不变
    def __enter__(self):
        return self

    # 修改 __exit__ 不再关闭底层连接
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 不再需要 self.close_connection() 因为连接池管理连接
        # 如果 close_connection 内部有其他清理逻辑，可以保留调用
        # self.close_connection()
        pass

# --- 安装依赖 ---
# 你需要安装 DBUtils 库:
# pip install DBUtils

if __name__ == "__main__":
    # 测试代码
    chat_id_1 = "Chat_9c7a646754d74ecdaf38"
    chat_id_2 = "Chat_209d586d42f74833af32"
    chat_id_3 = "Chat_1fb987229ebb4bcfb01c"

    # 使用上下文管理器自动处理连接获取和返回
    with WiseMatchDBConnector() as connector:
        resume = connector.get_resume(chat_id_3)
        # print("resume: ")
        # pprint(resume)

        # 测试处理后的数据块
        print("\n=== 处理后的数据块 ===")
        grouped_history, examine_dict, jd = connector.get_input_block(chat_id_1)
        # pprint(grouped_history[index])
        if '知识技能' in examine_dict:
             pprint(examine_dict['知识技能'])
        # pprint(examine_dict['价值观'])
        # pprint(examine_dict['身体/生理条件'])
        # pprint(examine_dict['性格特质/心理风险'])
        # pprint(examine_dict['兴趣/动机'])
        # pprint(examine_dict['语言表达能力'])
        # pprint(examine_dict['岗位胜任力'])
        # print(jd)

    # with 语句结束时，__exit__ 会被调用，但不会再关闭数据库连接，
    # 因为连接在每次使用后（conn.close()）就已经返回给池了。
    # 连接池本身会保持活动状态，等待下一次使用。
