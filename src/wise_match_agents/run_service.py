"""
启动面试分析服务的脚本

运行方式:
1. 直接运行: python -m wise_match_agents.run_service
2. 使用Poetry: poetry run wise-match-server
3. 开发模式: python -m wise_match_agents.run_service --reload

环境变量:
- WISE_MATCH_ENV: 环境标识 (dev/prod)
- APP_HOST: 服务监听地址 (默认: 0.0.0.0)
- APP_PORT: 服务监听端口 (默认: 8000)
- LOG_LEVEL: 日志级别 (默认: INFO)
- MAX_WORKERS: 最大工作线程数 (默认: 10)

API接口示例:

curl -X POST "http://localhost:8000/analyze" \
  -H "Content-Type: application/json" \
  -d @request.json

curl -X GET "http://localhost:8000/health"
"""

import argparse
import os
import sys
import logging
from pathlib import Path

import uvicorn

from wise_match_agents.config.sql_config import get_app_config

# 配置日志
def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            # 可以添加文件日志处理器
            # logging.FileHandler('logs/app.log')
        ]
    )

    # 禁用一些噪音日志
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="启动面试分析服务",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default=os.getenv("WISE_MATCH_ENV", "dev"),
        help="环境标识"
    )

    parser.add_argument(
        "--host",
        default=os.getenv("APP_HOST", "0.0.0.0"),
        help="服务监听地址"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=int(os.getenv("APP_PORT", "8000")),
        help="服务监听端口"
    )

    parser.add_argument(
        "--workers",
        type=int,
        default=int(os.getenv("MAX_WORKERS", "1")),
        help="工作进程数 (生产环境建议设置为CPU核心数)"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用热重载 (仅开发环境)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=os.getenv("LOG_LEVEL", "INFO"),
        help="日志级别"
    )

    return parser.parse_args()


def validate_environment() -> None:
    """验证运行环境"""
    # 检查必要的目录
    project_root = Path(__file__).parent.parent.parent
    config_dir = project_root / "config"

    if not config_dir.exists():
        raise RuntimeError(f"配置目录不存在: {config_dir}")

    # 检查配置文件
    env = os.getenv("WISE_MATCH_ENV", "dev")
    config_file = config_dir / f"database_{env}.yaml"

    if not config_file.exists():
        raise RuntimeError(f"配置文件不存在: {config_file}")

    print(f"✅ 环境验证通过 - 环境: {env}")


def main() -> None:
    """主函数"""
    try:
        args = parse_args()

        # 设置环境变量
        os.environ["WISE_MATCH_ENV"] = args.env

        # 设置日志
        setup_logging(args.log_level)
        logger = logging.getLogger(__name__)

        # 验证环境
        validate_environment()

        # 获取应用配置
        app_config = get_app_config(args.env)

        logger.info("🚀 启动面试分析服务...")
        logger.info(f"   环境: {args.env}")
        logger.info(f"   地址: http://{args.host}:{args.port}")
        logger.info(f"   工作进程数: {args.workers}")
        logger.info(f"   日志级别: {args.log_level}")

        # 生产环境不允许热重载
        if args.reload and args.env == "prod":
            logger.warning("⚠️  生产环境不建议使用热重载功能")
            args.reload = False

        # 启动服务
        uvicorn.run(
            "wise_match_agents.service_api:app",
            host=args.host,
            port=args.port,
            workers=args.workers if not args.reload else 1,  # 热重载模式只能单进程
            reload=args.reload,
            access_log=args.env == "dev",
            log_level=args.log_level.lower(),
            # 生产环境的其他配置
            **({
                "loop": "uvloop",  # 高性能事件循环
                "http": "httptools",  # 高性能HTTP解析器
            } if args.env == "prod" else {})
        )

    except KeyboardInterrupt:
        logger.info("👋 服务被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
